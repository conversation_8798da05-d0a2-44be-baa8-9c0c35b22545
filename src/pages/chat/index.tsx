import BottomInput from '@/components/BottomInput'
import { currentThreadIdState, Message, messgaeListState } from '@/store/chat'
import useObjAtom from '@/hooks/useObjAtom'
import { ChatMarkdown } from '@/components/ChatMarkdown'
import { ChatTool } from '@/components/ChatTool'
import Taro, { useDidShow, useDidHide } from '@tarojs/taro'
import { useCallback, useEffect, useRef } from 'react'
import useObjState from '@/hooks/useObjState'
import { userinfoState } from '@/store/global'
import { useAtomValue } from 'jotai'
import { decodeUint8Array } from '@/utils'
import { synthesizeQwenTTSRealtime } from '@/utils/qwenTTS'
import NavBarTitle from './components/NavBarTitle'
import { getMiniStsToken } from '@/api/global'

const Index = () => {
  const isWx = Taro.getEnv() === 'WEAPP'
  const chatListRef = useRef<HTMLDivElement>(null)
  const scrollToBottomRef = useRef<boolean>(false)
  const isScrollToBottom = useObjState(true) // 记录是否滚动到底部，用于判断是否需要滚动到底部
  const currentThreadId = useObjAtom(currentThreadIdState)
  const messgaeList = useObjAtom(messgaeListState)
  const keyboardHeight = useObjState(0) // 键盘高度
  const userinfo = useAtomValue(userinfoState)
  const title = useObjState('')
  const loading = useObjState(false)

  // 用于记录是否是第一个分块
  const isFirstChunk = useRef(true)
  // 用于记录是否是最后一个分块
  const isLastChunk = useRef(false)
  // 用于缓存未完成的 JSON 数据
  const incompleteDataRef = useRef<string>('')

  const init = useCallback(async () => {
    let threadId = currentThreadId.val
    if (!threadId) {
      threadId = await new Promise((resolve) => {
        Taro.request({
          url: `${process.env.TARO_APP_API_AI}/agent/threads`,
          method: 'POST',
          data: { metadata: { userId: userinfo?.userId || 1 } }
        }).then((res) => {
          resolve(res.data.thread_id)
        })
      })
      currentThreadId.set(threadId)
      Taro.setStorageSync('threadId', threadId)
    }
    Taro.request({
      url: `${process.env.TARO_APP_API_AI}/agent/threads/${threadId}/history`,
      method: 'POST',
      data: { limit: 1000 }
    }).then((res) => {
      messgaeList.set(res.data[0]?.values?.messages || [])
      const content = res.data[0]?.values?.messages[0].content
      // 判断content是否为图片，如果是图片，赋值为图片文件
      if (content && content.includes('img:')) {
        title.set('图片')
      } else {
        title.set(content || '')
      }
      // 延迟滚动到底部
      setTimeout(() => {
        scrollToBottom?.()
      }, 100)
      Taro.getStorage({ key: 'chatMessageData' })
        .then((storage) => {
          console.log(storage.data)
          if (storage.data) {
            // 延迟发送消息，确保页面已经渲染完成
            setTimeout(() => {
              sendMessage(storage.data.text, storage.data.type || 'text')
            }, 200)
            Taro.removeStorage({ key: 'chatMessageData' })
          }
        })
        .catch((err) => {
          console.log(err)
        })
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentThreadId, messgaeList, userinfo])

  useEffect(() => {
    init()
  }, [])

  // 监听键盘高度变化
  useEffect(() => {
    if (Taro.getEnv() === 'WEAPP') {
      const onKeyboardHeightChange = (res: { height: number }) => {
        console.log('chat页面 键盘高度变化:', res.height)
        keyboardHeight.set(res.height)
      }

      Taro.onKeyboardHeightChange(onKeyboardHeightChange)

      return () => {}
    }
  }, [])

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    if (!scrollToBottomRef.current) {
      scrollToBottomRef.current = true
      setTimeout(() => {
        if (isWx) {
          // 微信小程序环境，使用 pageScrollTo
          Taro.createSelectorQuery()
            .select('.chat-container')
            .boundingClientRect((rect) => {
              if (rect && !Array.isArray(rect)) {
                Taro.pageScrollTo({
                  scrollTop: rect.height,
                  duration: 300
                })
              }
            })
            .exec()
        } else {
          // H5 环境，直接操作 DOM
          chatListRef.current?.scrollTo({
            top: chatListRef.current.scrollHeight,
            behavior: 'smooth'
          })
        }
        isScrollToBottom.set(true)
        scrollToBottomRef.current = false
      }, 250)
    }
  }, [isWx, isScrollToBottom])

  //   {
  //   "event": "updates",
  //   "data": {
  //     "__interrupt__": [
  //       {
  //         "value": {
  //           "question": "Is this correct?",
  //           "tool_call": {
  //             "name": "navigate_tool",
  //             "args": { "action": "Home", "arguments": {} },
  //             "id": "call_t2z8hgrl32n6z473n2m0naui",
  //             "type": "tool_call"
  //           }
  //         },
  //         "resumable": true,
  //         "ns": ["tools:60d1b3e0-c84d-cf57-5529-0a5d5df31465"],
  //         "when": "during"
  //       }
  //     ]
  //   }
  // }

  // 解析分块数据的函数
  const parseChunkData = useCallback((chunkText: string) => {
    if (incompleteDataRef.current) {
      console.log('拼接缓存数据')
      chunkText = incompleteDataRef.current + chunkText
      incompleteDataRef.current = ''
    }

    const entries: any[] = []

    // 按行分割数据
    const lines = chunkText.split('\n').filter((line) => line.trim())
    console.log('lines', lines)

    let currentEntry: any = {}

    for (const line of lines) {
      if (line.startsWith('event:')) {
        // 如果已有完整的entry，先保存
        if (currentEntry.event && currentEntry.data) {
          entries.push(currentEntry)
          currentEntry = {}
        }
        currentEntry.event = line.replace('event:', '').trim()
      } else if (line.startsWith('data:')) {
        try {
          const dataStr = line.replace('data:', '').trim()
          currentEntry.data = JSON.parse(dataStr)
        } catch (e) {
          // console.warn('解析data失败:', line)
          // 如果解析失败，可能是数据不完整，缓存起来
          incompleteDataRef.current = chunkText
          return []
        }
      } else if (line.startsWith('id:')) {
        currentEntry.id = line.replace('id:', '').trim()
      }
    }

    // 保存最后一个entry
    if (currentEntry.event && currentEntry.data) {
      entries.push(currentEntry)
    }
    console.log('entries', entries)
    return entries
  }, [])

  // 处理消息条目的函数
  const processMessageEntry = useCallback(
    (entry: any) => {
      if (!entry.data) return

      let messageData: Message

      // 根据数据格式提取消息内容
      if (Array.isArray(entry.data)) {
        // 如果是数组，取下标0的数据
        messageData = entry.data[0]
      } else if (entry.data.messages && Array.isArray(entry.data.messages)) {
        // 如果是对象且有messages数组，取messages[0]的数据
        messageData = entry.data.messages[entry.data.messages.length - 1]
      } else {
        return
      }

      if (!messageData || !messageData.id || messageData.content === undefined) return

      console.log('messageData', messageData)
      const { id, content, type, response_metadata } = messageData

      // 更新messageList
      const currentMessages = messgaeList.get()
      const existingMessageIndex = currentMessages.findIndex((msg) => msg.id === id)

      if (existingMessageIndex >= 0) {
        // 更新已存在的消息
        const updatedMessages = [...currentMessages]
        updatedMessages[existingMessageIndex] = {
          ...updatedMessages[existingMessageIndex],
          content: updatedMessages[existingMessageIndex].content + content,
          response_metadata: response_metadata || updatedMessages[existingMessageIndex].response_metadata
        }
        messgaeList.set(updatedMessages)
      } else {
        // 添加新消息
        const newMessage = {
          content: content,
          additional_kwargs: {},
          response_metadata: response_metadata || {},
          type: type === 'AIMessageChunk' ? 'ai' : type,
          name: null,
          id,
          example: false
        }
        messgaeList.set([...currentMessages, newMessage])
      }

      // 实时TTS播放（仅H5端，且AI消息）
      if ((type === 'ai' || type === 'AIMessageChunk') && content) {
        // 只处理纯文本
        const plainText = content.replace(/!\[.*?\]\(.*?\)/g, '') // 去除markdown图片
        synthesizeQwenTTSRealtime(plainText)
      }

      // 滚动到底部
      if (isScrollToBottom) {
        scrollToBottom?.()
      }
    },
    [isScrollToBottom, messgaeList, scrollToBottom]
  )

  // send
  const sendMessage = (text: string, type: 'text' | 'image' = 'text') => {
    // 如果当前路由不是chat，则跳转到chat
    const routes = Taro.getCurrentPages()
    if (routes[routes.length - 1].route !== 'pages/chat/index') {
      Taro.navigateTo({ url: '/pages/chat/index' })
    }
    if (!title.val) {
      if (type === 'image') {
        title.set('图片')
      } else {
        title.set(text || '')
      }
    }
    if (Taro.getEnv() === 'WEAPP') {
      // 微信小程序环境下的实现
      // const requestTask = Taro.request({
      //   url: `https://test-ai.wode.me/agent/threads/${currentThreadId.val}/runs/stream`,
      //   method: 'POST',
      //   data: {
      //     input: { messages: [{ type: 'human', content: text }] },
      //     stream_mode: ['messages-tuple', 'values'],
      //     assistant_id: process.env.TARO_APP_ASSISTANT_ID,
      //     on_disconnect: 'cancel'
      //   },
      //   enableChunked: true
      // })

      // @ts-ignore
      const requestTask = wx.request({
        url: `${process.env.TARO_APP_API_AI}/agent/threads/${currentThreadId.val}/runs/stream`, //仅为示例，并非真实的接口地址
        method: 'POST',
        data: {
          input: { messages: [{ type: 'human', content: text }] },
          stream_mode: ['messages-tuple', 'values'],
          assistant_id: process.env.TARO_APP_ASSISTANT_ID,
          on_disconnect: 'cancel'
        },
        enableChunked: true
      })

      loading.set(true)

      isFirstChunk.current = true
      isLastChunk.current = false
      console.log('requestTask', requestTask)
      requestTask.onChunkReceived((res) => {
        let chunkText = decodeUint8Array(res.data)
        // console.log('chunkText', chunkText)

        // 解析分块数据，每个分块可能包含多条数据
        const dataEntries = parseChunkData(chunkText)

        // 处理数据条目
        dataEntries.forEach((entry, index) => {
          // 跳过第一个分块的第一条数据（任务执行次数）
          if (isFirstChunk.current && index === 0) {
            console.log('跳过第一个分块的第一条数据（任务执行次数）:', entry)
            isFirstChunk.current = false
            return
          }

          // 判断是否为最后一条数据
          const isLastEntry = index === dataEntries.length - 1
          // 检查是否为汇总数据，如果是则跳过处理
          if (isLastEntry && entry.event === 'values' && isLastChunk.current) {
            console.log('跳过汇总数据:', entry)
            const messageData = entry.data.messages[entry.data.messages.length - 1]
            if (messageData.tool_calls && messageData.tool_calls.length) {
              const toolCalls = messageData.tool_calls[0]
              if (toolCalls.name === 'gen_canvas_tool') {
                // 添加新消息
                const newMessage: Message = {
                  tool_calls: messageData.tool_calls,
                  content: '',
                  additional_kwargs: {},
                  response_metadata: messageData.response_metadata || {},
                  type: 'ai',
                  name: null,
                  id: messageData.id,
                  example: false
                }
                messgaeList.set((v) => [...v, newMessage])
              }
            }

            loading.set(false)
            return
          }

          // 检查是否为最后一个分块
          if (!isLastChunk.current && entry.event === 'values') {
            isLastChunk.current = true
          }

          // 处理普通消息数据
          processMessageEntry(entry)
        })
      })

      // 监听请求完成事件
      requestTask.onHeadersReceived(() => {
        console.log('请求头接收完成')
      })
    } else {
      // H5 环境下使用 fetch 实现
      const url = `${process.env.TARO_APP_API_AI}/agent/threads/${currentThreadId.val}/runs/stream`
      const body = JSON.stringify({
        input: { messages: [{ type: 'human', content: text }] },
        stream_mode: ['messages-tuple', 'values'],
        assistant_id: process.env.TARO_APP_ASSISTANT_ID,
        on_disconnect: 'cancel'
      })

      isFirstChunk.current = true
      isLastChunk.current = false

      fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body
      })
        .then((response) => {
          const reader = response.body?.getReader()
          const decoder = new TextDecoder('utf-8')

          const readStream = async () => {
            if (!reader) return

            let done = false
            while (!done) {
              const { value, done: readerDone } = await reader.read()
              done = readerDone

              if (value) {
                const chunkText = decoder.decode(value, { stream: true })
                // console.log('chunkText', chunkText)

                // 解析分块数据，每个分块可能包含多条数据
                const dataEntries = parseChunkData(chunkText)

                // 处理数据条目
                dataEntries.forEach((entry, index) => {
                  // 跳过第一个分块的第一条数据（任务执行次数）
                  if (isFirstChunk.current && index === 0) {
                    console.log('跳过第一个分块的第一条数据（任务执行次数）:', entry)
                    isFirstChunk.current = false
                    return
                  }

                  // 判断是否为最后一条数据
                  const isLastEntry = index === dataEntries.length - 1
                  // 检查是否为汇总数据，如果是则跳过处理
                  if (isLastEntry && entry.event === 'values' && isLastChunk.current) {
                    console.log('跳过汇总数据:', entry)
                    const messageData = entry.data.messages[entry.data.messages.length - 1]
                    if (messageData.tool_calls && messageData.tool_calls.length) {
                      const toolCalls = messageData.tool_calls[0]
                      if (toolCalls.name === 'gen_canvas_tool') {
                        // 添加新消息
                        const newMessage: Message = {
                          tool_calls: messageData.tool_calls,
                          content: '',
                          additional_kwargs: {},
                          response_metadata: messageData.response_metadata || {},
                          type: 'ai',
                          name: null,
                          id: messageData.id,
                          example: false
                        }
                        upload(newMessage)
                      }
                    }

                    return
                  }

                  // 检查是否为最后一个分块
                  if (!isLastChunk.current && entry.event === 'values') {
                    isLastChunk.current = true
                  }

                  // 处理普通消息数据
                  processMessageEntry(entry)
                })
              }
            }
          }

          readStream()
        })
        .catch((error) => {
          console.error('Fetch 请求错误:', error)
        })
    }
  }

  const upload = async (diyData) => {
    console.log('data', diyData)

    const res = await Taro.request({
      url: `${process.env.TARO_APP_API_AI}/server/v1/genimage/preview`,
      method: 'POST',
      data: {
        model: [
          {
            x: '0.00%',
            y: '0.00%',
            w: '100.00%',
            url: 'https://vip-star.meishubao.com/dev/img/x.png',
            originWidth: 750,
            originHeight: 750,
            type: 'Image',
            button_2_Text: '替换',
            canDo: true,
            canFlip: true,
            flip: {
              x: 1,
              y: 1
            },
            themeColor: 'red',
            reference: 'stage',
            zIndex: -1,
            actived: false,
            clicked: false,
            queue: true,
            blendMode: 'source-over',
            rotateRadius: 0,
            rotateAroundCenter: true,
            stroke: true,
            fill: true,
            strokeStyle: 'black',
            fillStyle: 'transparent',
            fillRule: 'nonzero',
            outline: false,
            canMove: true,
            canRotate: true,
            canScale: true,
            canUp: true,
            canRemove: true,
            canClick: true,
            canActive: true,
            canExport: true,
            canSort: true
          }
        ],
        custom: [
          {
            x: '26.11%',
            y: '48.33%',
            button_2_Text: '编辑',
            canDo: true,
            canFlip: true,
            flip: {
              x: 1,
              y: 1
            },
            maxWidth: '26.32%',
            maxHeight: '18.80%',
            text: '口',
            fontSize: '32.51%',
            fontFamily: 'Arial',
            textColor: 'red',
            bold: false,
            italic: false,
            textDecoration: false,
            textBaseline: 'middle',
            textAlign: 'center',
            background: true,
            cornerRadius: 5,
            backgroundColor: 'transparent',
            border: 0,
            borderColor: '#000000',
            padding: {
              top: 0,
              bottom: 0,
              left: 0,
              right: 0
            },
            direction: 'horizontal',
            letterSpacing: 0,
            lineSpacing: 0,
            spacingLeft: 1,
            spacingTop: 1,
            type: 'Text',
            reference: 'view',
            clicked: false,
            actived: false,
            zIndex: 0,
            queue: true,
            blendMode: 'source-over',
            rotateRadius: 0,
            rotateAroundCenter: true,
            stroke: true,
            fill: true,
            strokeStyle: 'black',
            fillStyle: 'transparent',
            fillRule: 'nonzero',
            outline: false,
            canMove: true,
            canRotate: true,
            canScale: true,
            canUp: true,
            canRemove: true,
            canClick: true,
            canActive: true,
            canExport: true,
            canSort: true
          },
          {
            x: '26.11%',
            y: '48.33%',
            w: '49.00%',
            url: 'https://s2.xiaoxiongmeishu.com/courseware/h5/development/upload/user_1752735804171/4129.png',
            originWidth: 176,
            originHeight: 176,
            type: 'Image',
            button_2_Text: '裁剪图片',
            canDo: true,
            canFlip: true,
            flip: {
              x: 1,
              y: 1
            },
            reference: 'view',
            rotateAroundCenter: true,
            clicked: true,
            actived: false,
            zIndex: 1,
            queue: true,
            blendMode: 'source-over',
            rotateRadius: 0,
            stroke: true,
            fill: true,
            strokeStyle: 'black',
            fillStyle: 'transparent',
            fillRule: 'nonzero',
            outline: false,
            canMove: true,
            canRotate: true,
            canScale: true,
            canUp: true,
            canRemove: true,
            canClick: true,
            canActive: true,
            canExport: true,
            canSort: true
          }
        ],
        view: {
          x: '0.00%',
          y: '0.00%',
          w: '100.00%',
          url: 'https://vip-star.meishubao.com/dev/img/viewx.png',
          originWidth: 750,
          originHeight: 750,
          type: 'Image',
          button_2_Text: 'special',
          canDo: true,
          canFlip: true,
          flip: {
            x: 1,
            y: 1
          },
          blendMode: 'destination-in',
          reference: 'stage',
          outline: false,
          zIndex: -1,
          actived: false,
          clicked: false,
          queue: true,
          rotateRadius: 0,
          rotateAroundCenter: true,
          stroke: true,
          fill: true,
          strokeStyle: 'black',
          fillStyle: 'transparent',
          fillRule: 'nonzero',
          canMove: true,
          canRotate: true,
          canScale: true,
          canUp: true,
          canRemove: true,
          canClick: true,
          canActive: true,
          canExport: true,
          canSort: true
        }
      }
    })

    console.log('res', res.data.output)
    const data = {
      preview: res.data.output,
      diyData: diyData
    }

    

    //  messgaeList.set((v) => [...v, newMessage])

    // const filePath = res.tempFilePaths[0]
    // console.log('filePath', filePath)
    // if (!filePath) {
    //   return
    // }
    // const stsRes = await getMiniStsToken()
    // const sts = stsRes.data
    // console.log('sts', sts)

    // const formData = {
    //   key: `ai-mp-wode-shop/${process.env.TARO_APP_ENV}/${Date.now()}.png`, // 上传文件名称
    //   policy: sts.policy, // 表单域
    //   'x-oss-signature-version': sts.x_oss_signature_version, // 指定签名的版本和算法
    //   'x-oss-credential': sts.x_oss_credential, // 指明派生密钥的参数集
    //   'x-oss-date': sts.x_oss_date, // 请求的时间
    //   'x-oss-signature': sts.signature, // 签名认证描述信息
    //   'x-oss-security-token': sts.security_token, // 安全令牌
    //   success_action_status: '200' // 上传成功后响应状态码
    // }
    // Taro.uploadFile({
    //   url: 'https://art-meta.oss-cn-hangzhou.aliyuncs.com',
    //   filePath: filePath,
    //   name: 'file', // 固定值为file
    //   formData: formData,
    //   withCredentials: false
    // }).then((uploadRes) => {
    //   console.log('uploadRes', uploadRes)
    //   if (uploadRes.statusCode === 200) {
    //     const imageUrl = `${sts.cdn_url}/${formData.key}`
    //     const markdownImage = `img:${imageUrl}`

    //     sendMessage(markdownImage, 'image')
    //   } else {
    //     console.error('Image upload failed:', uploadRes)
    //   }
    // })
  }

  // 滚动事件
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10
      console.log('first', scrollHeight, clientHeight, scrollTop)
      isScrollToBottom.set(isAtBottom)
    },
    [isScrollToBottom]
  )

  return (
    <>
      <div className="w-full h-full bg-white overflow-hidden flex flex-col">
        <NavBarTitle title={title.val} />
        <div onClick={() => upload(1)}>AAA</div>
        <div ref={chatListRef} onScroll={handleScroll} className="chat-container relative flex-1 overflow-auto pt-[30px] pb-[172px]">
          {messgaeList.val.map((message, index) => {
            const isHuman = message.type === 'human'
            const containsImage = /!\[.*?\]\(.*?\)/.test(message.content) // 检测 Markdown 是否包含图片

            return (
              <div key={index} className={`flex rounded-[20px] ${isHuman ? 'justify-end mx-[20px]' : 'justify-start'} mb-2`}>
                <div
                  className={`px-[20px] py-[10px] text-[28px] rounded-lg w-auto ${
                    isHuman ? 'bg-[#000000] text-white' : 'text-black'
                  } ${isHuman && containsImage ? 'flex flex-col items-end' : ''}`} // 添加样式
                >
                  <ChatMarkdown content={message.content || ''} position={isHuman ? 'right' : 'left'} />
                  {message.tool_calls && <ChatTool tool_calls={message.tool_calls} id={message.id} />}
                </div>
              </div>
            )
          })}
        </div>
        {/* {isWx && <div className="h-[154px]"></div>} */}
      </div>
      <div
        className="fixed z-50 left-0 w-full h-[112px] transition-all duration-300 ease-in-out"
        style={{
          bottom: keyboardHeight.val > 0 ? `${keyboardHeight.val + 20}px` : '34px'
        }}
      >
        <BottomInput sendMessage={sendMessage} />
      </div>
    </>
  )
}

export default Index
