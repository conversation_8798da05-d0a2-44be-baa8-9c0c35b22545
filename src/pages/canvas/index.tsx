import useObj<PERSON>tom from '@/hooks/useObjAtom'
import { userinfoState } from '@/store/global'
import { WebView } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'

const TShirtCanvas = () => {
  const router = useRouter()
  const { id, jsonUrl } = router.params
  let token = Taro.getStorageSync('__weapp_token__') as string
  const newToken = token.split(' ')
  const tokenEnd = newToken[newToken.length - 1]
  const userinfo = useObjAtom(userinfoState)

  return (
    <>
      <WebView
        src={`https://test.h5.wode.me/canvas?id=${id || ''}&jsonUrl=${jsonUrl}&userId=${userinfo.val?.userId || ''}&token=${tokenEnd}`}
      />
    </>
  )
}

export default TShirtCanvas
