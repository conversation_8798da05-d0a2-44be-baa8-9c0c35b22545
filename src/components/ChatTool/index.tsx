import { memo } from 'react'
import Taro from '@tarojs/taro'

export const ChatTool = memo(({ tool_calls, id }: { tool_calls: any[]; id: string }) => {
  console.log('tool_calls', tool_calls)
  const imgs = tool_calls

  return (
    <div className="bg-[#EFEFF2] w-[500px] h-[500px] rounded-[16px]">
      <img className="w-[300px] h-[300px]" src={imgs[0].args.canvas.view.url} alt="" />
      <div
        onClick={() => {
          Taro.navigateTo({
            url: '/pages/canvas/index?jsonUrl=123'
          })
        }}
      >
        马上设计
      </div>
    </div>
  )
})
