import { memo, useMemo } from 'react'

export const ChatMarkdown = memo(({ content, position }: { content: string; position: 'left' | 'right' }) => {
  // 将字符串中的 \n 转换为真实的换行符
  const processedContent = useMemo(() => {
    // 如果内容中包含 \n 字符序列，将其替换为真实的换行符
    if (content && content.includes('\\n')) {
      return content.replace(/\\n/g, '\n')
    }
    return content
  }, [content])

  return processedContent
})
